Source System,Source View,Source Field,Source Alias,Staging Schema,Staging Table,Staging Field,DWH Schema,DWH Table,DWH Field,Transformation Logic,Data Type,Status,Issues/Gaps
LIMS,VW_CM_SUMMARY_RSLT,s.SAMPLE_NUMBER,,staging,chemistry_routine,sample_id,dwh,dim_sample,sample_id,Direct mapping,VARCHAR(50),Mapped,
LIMS,VW_CM_SUMMARY_RSLT,t.TEST_NUMBER,,staging,chemistry_routine,test_id,dwh,dim_test,test_id,Direct mapping,VARCHAR(50),Mapped,
LIMS,VW_CM_SUMMARY_RSLT,tc.IT_DESCRIPTION,name,staging,chemistry_routine,analyte_name,dwh,dim_analyte,analyte_name,Direct mapping,VARCHAR(200),Mapped,
LIMS,VW_CM_SUMMARY_RSLT,r.ENTRY,rawResult,staging,chemistry_routine,result_value,dwh,fact_test_results,result_value,Numeric conversion,"NUMERIC(15,6)",Mapped,
LIMS,VW_CM_SUMMARY_RSLT,r.C_FINAL_ID_ENTRY,FinalResult_Value,staging,chemistry_routine,detection_flag,dwh,fact_test_results,detection_flag,Detected/Not Detected,VARCHAR(20),Mapped,
LIMS,VW_CM_SUMMARY_RSLT,s.SAMPLED_DATE,,staging,chemistry_routine,sample_date,dwh,fact_test_results,sample_date_key,Date dimension lookup,DATE,Mapped,
LIMS,VW_CM_SUMMARY_RSLT,r.DATE_REVIEWED,resReviwedDate,staging,chemistry_routine,analysis_date,dwh,fact_test_results,analysis_date_key,Date dimension lookup,DATE,Mapped,
LIMS,VW_CM_SUMMARY_RSLT,sp.Location,,staging,chemistry_routine,sample_location,dwh,dim_location,location_name,Location dimension lookup,VARCHAR(200),Mapped,
LIMS,VW_CM_SUMMARY_RSLT,s.TEMPLATE,sampleTemplate,staging,chemistry_routine,sample_type,dwh,dim_sample,sample_type,Template classification,VARCHAR(100),Mapped,
LIMS,VW_CM_SUMMARY_RSLT,s.PROJECT,,staging,chemistry_routine,project_name,dwh,dim_sample,project_name,Direct mapping,VARCHAR(200),Mapped,
LIMS,VW_CM_SUMMARY_RSLT,sp.Latitude,,,,,dwh,dim_location,coordinates_lat,EXCLUDED per requirements,"NUMERIC(10,8)",Excluded,Geospatial excluded
LIMS,VW_CM_SUMMARY_RSLT,sp.Longitude,,,,,dwh,dim_location,coordinates_lng,EXCLUDED per requirements,"NUMERIC(11,8)",Excluded,Geospatial excluded
LIMS,VW_CM_SUMMARY_RSLT,sp.GISID,GIS_ID,,,,dwh,dim_location,location_id,GIS identifier,VARCHAR(50),Mapped,
LIMS,vw_special_cm_res,s.C_SERIAL_NO,Source,,,,,,,Serial number tracking,VARCHAR(50),Missing,Not captured in staging
LIMS,vw_special_cm_res,s.STATUS,SampleStatus,staging,chemistry_special,result_qualifier,dwh,fact_test_results,result_qualifier,Sample status,VARCHAR(20),Mapped,
LIMS,vw_special_cm_res,t.STATUS,TestStatus,,,,,,,Test status tracking,VARCHAR(20),Missing,Not captured in staging
LIMS,vw_special_cm_res,le.VALUE,sampleType,staging,chemistry_special,sample_type,dwh,dim_sample,sample_type,Enhanced sample type,VARCHAR(100),Mapped,
LIMS,vw_special_cm_res,tc.EL_DESCRIPTION,reported_name,staging,chemistry_special,special_test_category,dwh,dim_analyte,analyte_category,Special test category,VARCHAR(100),Mapped,
LIMS,VW_MB_SUMMARY_RSLT2,tc.IT_DESCRIPTION,name,staging,microbiology_routine,organism_name,dwh,dim_analyte,analyte_name,Organism name,VARCHAR(200),Mapped,
LIMS,VW_MB_SUMMARY_RSLT2,t.REPORTED_NAME,Result_Analysis1,staging,microbiology_routine,test_method,dwh,dim_test,test_method,Test method,VARCHAR(100),Mapped,
LIMS,VW_MB_SUMMARY_RSLT2,r.FORMATTED_ENTRY,FinalResult_Value,staging,microbiology_routine,result_value,dwh,fact_test_results,result_value,Standardized result,VARCHAR(100),Mapped,Needs standardization logic
LIMS,VW_MB_SUMMARY_RSLT2,r.C_FINAL_ID,,staging,microbiology_routine,result_qualifier,dwh,fact_test_results,result_qualifier,Result qualifier,VARCHAR(20),Mapped,
LIMS,VW_MB_SUMMARY_RSLT2,,,staging,microbiology_routine,ct_value,dwh,fact_test_results,ct_value,CT value for molecular tests,"NUMERIC(10,3)",Missing,Not in LIMS view
LIMS,VW_MB_SUMMARY_RSLT2,,,staging,microbiology_routine,gene_target,dwh,fact_test_results,gene_target,Gene target for molecular tests,VARCHAR(100),Missing,Not in LIMS view
LIMS,VW_MB_SUMMARY_RSLT2,,,staging,microbiology_routine,pcr_channel,dwh,fact_test_results,pcr_channel,PCR channel information,VARCHAR(20),Missing,Not in LIMS view
LIMS,vw_special_mb_res,r.ENTRY,FinalResult_Value,staging,microbiology_special,result_value,dwh,fact_test_results,result_value,Raw entry value,VARCHAR(100),Mapped,
LIMS,vw_special_mb_res,r.FORMATTED_ENTRY,actual_formatted_entry,,,,,,,Formatted entry,VARCHAR(100),Missing,Not captured in staging
LIMS,vw_special_mb_res,t.ANALYSIS,limsTestName,,,,,,,LIMS test name,VARCHAR(100),Missing,Not captured in staging
LIMS,vw_special_mb_res,tc.EL_DESCRIPTION,reported_name,staging,microbiology_special,special_test_category,dwh,dim_analyte,analyte_category,Special test category,VARCHAR(100),Mapped,
BRD,fact_biosurv_metrics,change_value,,,,,dwh,fact_test_results,,(m2-m1)/m1,"NUMERIC(15,6)",Missing,Change point calculation
BRD,fact_biosurv_metrics,change_percent,,,,,dwh,fact_test_results,,((m2-m1)/m1)*100,"NUMERIC(15,6)",Missing,Change percentage calculation
BRD,fact_biosurv_metrics,change_sign,,,,,dwh,fact_test_results,,Positive/Negative,VARCHAR(10),Missing,Change direction
BRD,fact_biosurv_metrics,risk_flag,,,,,dwh,fact_test_results,,Risk flag indicator,BOOLEAN,Missing,Risk assessment
BRD,fact_biosurv_metrics,risk_score,,,,,dwh,fact_test_results,,Weighted risk score,"NUMERIC(15,6)",Missing,Risk scoring
BRD,fact_biosurv_metrics,emerging_risk_flag,,,,,dwh,fact_test_results,,Emerging risk indicator,BOOLEAN,Missing,Emerging risk detection
BRD,fact_biosurv_metrics,anomaly_flag,,,,,dwh,fact_test_results,,Anomaly detection flag,BOOLEAN,Missing,AI anomaly detection
BRD,fact_biosurv_metrics,forecasting_json,,,,,dwh,fact_test_results,,Forecast results,JSONB,Missing,Time series forecasting
