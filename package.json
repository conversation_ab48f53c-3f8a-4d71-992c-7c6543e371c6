{"name": "m42-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/nlp-infotointell/m42-backend.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/nlp-infotointell/m42-backend/issues"}, "homepage": "https://github.com/nlp-infotointell/m42-backend#readme", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@sinclair/typebox": "^0.34.35", "fastify": "^5.4.0", "fastify-swagger": "^5.1.1", "pg": "^8.16.3"}, "devDependencies": {"@types/node": "^24.0.3", "@types/pg": "^8.15.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}