SELECT
    DISTINCT s.PROJECT,
    p.CUSTOMER,
    p.CLOSED,
    p.STATUS AS projStatus,
    p.C_DELIVERED_BY,
    p.C_RECD_DATE AS date_received,
    p.DATE_STARTED,
    p.DATE_COMPLETED,
    p.DATE_REVIEWED,
    s.SAMPLE_NUMBER,
    s.TEXT_ID,
    s.ORIGINAL_SAMPLE,
    s.SAMPLED_DATE,
    s.TEMPLATE AS sampleTemplate,
    t.ANALYSIS,
    t.TEST_NUMBER,
    t.GROUP_NAME AS Lab,
    r.RESULT_NUMBER,
    s.STATUS AS SampleStatus,
    t.STATUS AS TestStatus,
    CASE
        WHEN t.STATUS = 'I' THEN 'Incomplete'
        WHEN t.STATUS = 'P' THEN 'In-Progress'
        WHEN t.STATUS = 'C' THEN 'Complete'
        WHEN t.STATUS = 'A' THEN 'Authorised'
        WHEN t.STATUS = 'X' THEN 'Cancelled'
        WHEN t.STATUS = 'R' THEN 'Rejected'
    END AS TestStatusDesc,
    r.STATUS AS ResultStatus,
    CASE
        WHEN r.STATUS = 'N' THEN 'Not Entered'
        WHEN r.STATUS = 'E' THEN 'Entered'
        WHEN r.STATUS = 'M' THEN 'Modified'
        WHEN r.STATUS = 'A' THEN 'Authorised'
        WHEN r.STATUS = 'X' THEN 'Cancelled'
        WHEN r.STATUS = 'R' THEN 'Rejected'
    END AS ResultStatusDesc,
    r.ALIAS_NAME AS casNum,
    r.ENTRY AS rawResult,
    r.FORMATTED_ENTRY,
    r.C_FINAL_ID_ENTRY AS FinalResult_Value,
    a.C_ANALYTE_GROUP AS Population,
    a.ANALYSIS_TYPE AS Analyte_Group,
    tc.EL_DESCRIPTION AS reported_name,
    tc.IT_DESCRIPTION AS name,
    tc.IT_DESCRIPTION AS Result_Analysis,
    tc.IT_DESCRIPTION AS Result_Analysis1,
    tc.IT_DESCRIPTION AS Final_Result,
    sp.Community AS Collection_Point,
    sp.Location,
    sp.Latitude,
    sp.Longitude,
    sp.GISID AS GIS_ID,
    sp.Name AS CatchmentArea,
    CASE
        WHEN tc.EL_DESCRIPTION IN (
            'Illicit Drugs',
            'Pharmaceutical & Personal Care Products',
            'Steroids and hormones',
            'SVOC',
            'VOC'
        ) THEN 'Organic Chemistry'
        WHEN tc.EL_DESCRIPTION IN (
            'Anions & Cations',
            'Metals by ICP MS',
            'Metals by ICP OES'
        ) THEN 'Inorganic Chemistry'
    END AS labName,
    r.CHANGED_ON AS resChangedOn,
    r.DATE_REVIEWED AS resReviewedDate
FROM
    SAMPLE AS s
    INNER JOIN TEST AS t ON t.SAMPLE_NUMBER = s.SAMPLE_NUMBER
    INNER JOIN RESULT AS r ON r.TEST_NUMBER = t.TEST_NUMBER
    INNER JOIN PROJECT AS p ON p.NAME = s.PROJECT
    INNER JOIN T_CAS_NUMBER AS tc ON tc.NAME = r.ALIAS_NAME
    INNER JOIN SAMPLING_POINTS AS sp ON s.c_gisid = sp.GISID
    INNER JOIN ANALYSIS AS a ON a.NAME = t.ANALYSIS
    AND a.VERSION = t.VERSION
WHERE
    (s.TEMPLATE IN ('WWML', 'WASTE_WATER_CHLD'))
    AND (t.GROUP_NAME = 'CHEM')
    AND (
        a.C_ANALYTE_GROUP IN ('POPULATION', 'NONE_POPULATION')
    )
    AND (a.ACTIVE = 'T')
    AND (sp.GISID IS NOT NULL)