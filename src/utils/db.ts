// db.js
import { Pool, PoolClient } from "pg";

let clientAgent: PoolClient;
const run = async () =>
  await new Pool({
    connectionString:
      "************************************************************",
  })
    .connect()
    .then((client: PoolClient) => {
      // db = client;
      console.log("DB connected");
      clientAgent = client;
      return client;
    });

// const agent = await run();

export const query = async (q: string): Promise<any[]> => {
  console.log("-------------------Query-----------------------");
  console.log(q);
  console.log("-------------------Query-----------------------");

  return (await clientAgent.query(q)).rows;
};
// let db: PoolClient | null = null;
// export { db };
export default run;
