export const showTrue = (res: any, ...vals: any[]) => {
  if (vals.every(Boolean)) return res;
};

export const calculateComparisonDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  const daysDifference = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
  
  const comparisonEnd = new Date(start);
  comparisonEnd.setDate(comparisonEnd.getDate() - 1);
  
  const comparisonStart = new Date(comparisonEnd);
  comparisonStart.setDate(comparisonStart.getDate() - daysDifference + 1);
  
  return {
    comparison_start_date: comparisonStart.toISOString().split('T')[0],
    comparison_end_date: comparisonEnd.toISOString().split('T')[0]
  };
};
