import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { risk_trends_cards } from "./cards";
import { allWidgets } from "../../config";

const riskTrendsRoute: FastifyPluginAsync = async (fastify) => {
  const QuerySchema = Type.Object({
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    level: Type.Optional(
      Type.String({
        enum: ["mid", "high", "tech"],
      })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    cards: Type.Optional(
      Type.String({
        description: "Comma-separated list of card IDs (1,2,3)",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
  });

  fastify.get(
    "/risk_trends_cards/all",
    {
      schema: {
        tags: ["risk_trends_cards_group"],
        summary: "Get all risk_trends_cards",
        response: {
          200: Type.Array(
            Type.Object({
              id: Type.Number(),
              name: Type.String(),
            })
          ),
        },
      },
    },
    async (request) => {
      return Object.keys(allWidgets.risk_trends_cards).map((i) => {
        return {
          ...allWidgets.risk_trends_cards[i].info,
        };
      });
    }
  );

  fastify.get(
    "/risk_trends_cards",
    {
      schema: {
        tags: ["risk_trends_cards_group"],
        summary: "Get risk trends cards by Ids",
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const {
        level,
        start_date,
        end_date,
        cards,
        analyte,
        category,
        group,
        location,
        sample_type,
      } = request.query as Static<typeof QuerySchema>;

      const cardsIds = cards?.split(",");

      const results = await Promise.all(
        Object.keys(allWidgets.risk_trends_cards).map(async (i) => {
          if (!cardsIds || !cardsIds.length || cardsIds.includes(String(i))) {
            const cardRes = await allWidgets.risk_trends_cards[
              i as keyof (typeof allWidgets)["risk_trends_cards"]
            ].get({
              start_date,
              end_date,
              cards,
              analyte,
              category,
              group,
              location,
              sample_type,
              level,
            });
            return {
              ...cardRes,
            };
          }
          return null;
        })
      );

      return results.filter(Boolean);
    }
  );
};

export default riskTrendsRoute;
