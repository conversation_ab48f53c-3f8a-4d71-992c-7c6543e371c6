import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { analyte_insights_cards } from "./cards";

const analyteInsightsRoute: FastifyPluginAsync = async (fastify) => {
  const ParamsSchema = Type.Object({
    level: Type.String({
      enum: ["mid", "high", "tech"],
    }),
  });

  const QuerySchema = Type.Object({
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    cards: Type.Optional(
      Type.String({
        description: "Comma-separated list of card IDs (1,2,3)",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
  });

  fastify.get(
    "/analyte_insights_cards/all",
    {
      schema: {
        tags: ["analyte_insights_cards_group"],
        summary: "Get all analyte_insights_cards",
        response: {
          200: Type.Array(
            Type.Object({
              id: Type.Number(),
              name: Type.String(),
            })
          ),
        },
      },
    },
    async (request) => {
      return Object.keys(analyte_insights_cards).map((i) => {
        return {
          id: i,
          name: analyte_insights_cards[i].name,
        };
      });
    }
  );

  fastify.get(
    "/analyte_insights_cards",
    {
      schema: {
        tags: ["analyte_insights_cards_group"],
        summary: "Get analyte insights cards by Ids",
        // params: ParamsSchema,
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const { level } = request.params as Static<typeof ParamsSchema>;
      const { start_date, end_date, cards } = request.query as Static<
        typeof QuerySchema
      >;

      if (!cards || !cards?.trim()) {
        return analyte_insights_cards;
      }
      const cardsIds = cards.split(",");
      return Object.keys(analyte_insights_cards)
        .map((i) => {
          if (cardsIds.includes(String(i)))
            return {
              ...analyte_insights_cards[i],
            };
        })
        .filter(Boolean);
    }
  );
};

export default analyteInsightsRoute;
