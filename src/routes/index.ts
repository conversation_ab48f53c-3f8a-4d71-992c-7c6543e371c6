import { FastifyInstance } from "fastify";
import summaryRoute from "./sample_overview_cards";
import riskTrendsRoute from "./risk_trends_cards";
import analyteInsightsRoute from "./analyte_insights_cards";
import plotsRoute from "./plots";
import testRoute from "./test";
import lookupRoute from "./lookup";
import mapsRoute from "./maps";

export async function registerRoutes(fastify: FastifyInstance) {
  fastify.register(testRoute, { prefix: "/dashboard" });
  fastify.register(summaryRoute, { prefix: "/dashboard" });
  fastify.register(riskTrendsRoute, { prefix: "/dashboard" });
  fastify.register(analyteInsightsRoute, { prefix: "/dashboard" });
  fastify.register(plotsRoute, { prefix: "/dashboard" });
  fastify.register(lookupRoute, { prefix: "/dashboard" });
  fastify.register(mapsRoute, { prefix: "/dashboard" });
}
