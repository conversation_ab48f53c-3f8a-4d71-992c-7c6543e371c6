import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { mapsConfig } from "../../config/maps";

const mapsRoute: FastifyPluginAsync = async (fastify) => {
  const QuerySchema = Type.Object({
    level: Type.Optional(
      Type.String({
        enum: ["mid", "high", "tech"],
      })
    ),
    date_frame: Type.String({
      enum: ["week", "month", "quarter", "year"],
    }),

    collection: Type.String({
      enum: [
        "sample_overview_cards",
        "analyte_insights_cards",
        "risk_trends_cards",
      ],
    }),
    start_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    end_date: Type.Optional(
      Type.String({ format: "date", description: "e.g 2007-07-07" })
    ),
    card_id: Type.Optional(
      Type.String({
        description: "card ID",
      })
    ),
    sample_type: Type.Optional(
      Type.String({
        description: "sample_type",
      })
    ),
    location: Type.Optional(
      Type.String({
        description: "location",
      })
    ),
    analyte: Type.Optional(
      Type.String({
        description: "analyte",
      })
    ),
    group: Type.Optional(
      Type.String({
        description: "group",
      })
    ),
    category: Type.Optional(
      Type.String({
        description: "category",
      })
    ),
  });

  fastify.get(
    "/map",
    {
      schema: {
        tags: ["map"],
        summary: "Get map data for a card",
        querystring: QuerySchema,
      },
    },
    async (request) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame,
        analyte,
        category,
        group,
        sample_type,
        location,
        level,
      } = request.query as Static<typeof QuerySchema>;

      const mapsData = await Promise.all(
        mapsConfig
          .filter((m) => {
            return (
              ((m.card_ids.includes(card_id) || m.card_ids.includes("*")) &&
                m.collections.includes(collection)) ||
              m.collections.includes("*")
            );
          })
          .filter(Boolean)
          .map(
            async (map) =>
              await map.get({
                start_date,
                end_date,
                card_id,
                collection,
                date_frame,
                analyte,
                category,
                group,
                location,
                sample_type,
              })
          )
      );

      return mapsData;
    }
  );
};

export default mapsRoute;
