import { FastifyPluginAsync } from "fastify";
import { Type, Static } from "@sinclair/typebox";
import { query } from "../../utils/db";
import { showTrue } from "../../utils/utils";

const lookupRoute: FastifyPluginAsync = async (fastify) => {
  fastify.get(
    "/lookup",
    {
      schema: {
        tags: ["lookup"],
      },
    },
    async (request) => {
      const analyte = await query(`
     	select lab_classification as analyte_category, analyte_group, analyte_name  from dwh.dim_analyte group by lab_classification, analyte_group, analyte_name
      `);

      const location = await query(`
         select location_name from dwh.dim_location
        `);

      return {
        analyte,
        location,
      };
    }
  );
};

export default lookupRoute;
