import { query } from "../utils/db";
import { calculateComparisonDateRange } from "../utils/utils";

export const allWidgets = {
  sample_overview_cards: {
    "1": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const getLegend = () => {
          if (start_date && end_date) return `${start_date} to ${end_date}`;
          if (start_date) return `${start_date} onwards`;
          if (end_date) return `Until ${end_date}`;
          return "All Time";
        };

        const res = await query(`
          SELECT
            COUNT(DISTINCT ds.sample_key) as total_samples_collected,
            COUNT(DISTINCT ds.sample_key) ${getDateFilter(
              "dt.full_date"
            )} as period_samples,
            COUNT(DISTINCT ds.sample_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.dim_sample ds
          INNER JOIN dwh.fact_test_results fts ON ds.sample_key = fts.sample_key
          INNER JOIN dwh.dim_analyte a ON a.analyte_key = fts.analyte_key
          INNER JOIN dwh.dim_location dl ON fts.location_key = dl.location_key
          INNER JOIN dwh.dim_time dt ON fts.sample_date_key = dt.time_key
          WHERE 1=1
          ${getWhereDate()}
          ${analyte ? `AND (a.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (a.lab_classification = '${category}')` : ""}
          ${group ? `AND (a.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 1,
          name: "Total Samples Collected",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: getLegend(),
        };
      },
      info: {
        id: 1,
        name: "Total Samples Collected",
        description:
          "Total number of samples collected within the specified date range",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "2": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(DISTINCT f.sample_key) as total_samples_tested,
            COUNT(DISTINCT f.sample_key) ${getDateFilter(
              "dt.full_date"
            )} as period_samples,
            COUNT(DISTINCT f.sample_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source = 'LIMS_Chemistry_Routine'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 2,
          name: "Total Samples Tested",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Tested Samples",
        };
      },
      info: {
        id: 2,
        name: "Total Samples Tested",
        description: "Total number of samples that have been tested",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "3": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(DISTINCT dl.location_key) as total_collection_points,
            COUNT(DISTINCT dl.location_key) ${getDateFilter(
              "dt.full_date"
            )} as period_points,
            COUNT(DISTINCT dl.location_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_points
          FROM dwh.dim_location dl
          LEFT JOIN dwh.fact_test_results f ON dl.location_key = f.location_key
          LEFT JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          LEFT JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          LEFT JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE dl.is_active = TRUE
          AND dl.location_type = 'Sample Point'
          ${getWhereDate()}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
        `);

        const {
          period_points: currentValue,
          comparison_points: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 3,
          name: "Total Number of Collection Points",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Active Points",
        };
      },
      info: {
        id: 3,
        name: "Total Number of Collection Points",
        description: "Total number of active sample collection points",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "4": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(DISTINCT dt.full_date) as total_collection_days,
            COUNT(DISTINCT dt.full_date) ${getDateFilter(
              "dt.full_date"
            )} as period_days,
            COUNT(DISTINCT dt.full_date) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_days
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source = 'LIMS_Chemistry_Routine'
          ${getWhereDate()}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
        `);

        const { period_days: currentValue, comparison_days: comparisonValue } =
          res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 4,
          name: "Total Days of Sample Collection",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Collection Days",
        };
      },
      info: {
        id: 4,
        name: "Total Days of Sample Collection",
        description: "Total number of days with sample collection activity",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "5": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            f.data_source,
            COUNT(DISTINCT f.sample_key) as sample_count,
            COUNT(DISTINCT f.sample_key) ${getDateFilter(
              "dt.full_date"
            )} as period_count,
            COUNT(DISTINCT f.sample_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_count
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE 1=1
          ${getWhereDate()}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          GROUP BY f.data_source
          ORDER BY sample_count DESC
        `);

        const totalCount = res.reduce((sum, row) => sum + row.period_count, 0);
        const comparisonCount = res.reduce(
          (sum, row) => sum + row.comparison_count,
          0
        );
        const percentageChange =
          comparisonCount > 0
            ? ((totalCount - comparisonCount) / comparisonCount) * 100
            : 0;

        return {
          id: 5,
          name: "Routine and Ad-hoc Samples Tested",
          type: "single_number",
          value: totalCount,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Routine vs Ad-hoc",
          breakdown: res.map((row) => ({
            name: row.data_source,
            count: row.period_count,
            percentage:
              totalCount > 0
                ? Math.round((row.period_count / totalCount) * 100)
                : 0,
          })),
        };
      },
      info: {
        id: 5,
        name: "Routine and Ad-hoc Samples Tested",
        description: "Total samples tested by data source type",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "6": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
          if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as total_samples_collected,
            COUNT(*) ${getDateFilter("ds.date_collected")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.dim_sample ds
          JOIN dwh.fact_test_results f ON ds.sample_key = f.sample_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          WHERE ds.date_collected IS NOT NULL
          ${getWhereDate()}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 6,
          name: "Total Samples Collected",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Collected Samples",
        };
      },
      info: {
        id: 6,
        name: "Total Samples Collected",
        description: "Total number of samples collected (with date_collected)",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "7": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
          if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as samples_to_test,
            COUNT(*) ${getDateFilter("ds.date_collected")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.dim_sample ds
          JOIN dwh.fact_test_results f ON ds.sample_key = f.sample_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          WHERE ds.sample_status IN ('P', 'I')
          ${getWhereDate()}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 7,
          name: "Samples to Test",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Pending & In Progress",
        };
      },
      info: {
        id: 7,
        name: "Samples to Test",
        description:
          "Total samples with status 'Pending' or 'In Progress' that need testing",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "8": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(DISTINCT f.sample_key) as samples_tested_positive,
            COUNT(DISTINCT f.sample_key) ${getDateFilter(
              "dt.full_date"
            )} as period_samples,
            COUNT(DISTINCT f.sample_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source = 'LIMS_Chemistry_Routine'
          AND f.detection_flag = 'Detected'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 8,
          name: "Samples Tested Positive",
          type: "percentage",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Detected Samples",
        };
      },
      info: {
        id: 8,
        name: "Samples Tested Positive",
        description: "Samples with 'Detected' analytes",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "9": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(DISTINCT f.sample_key) as samples_tested_negative,
            COUNT(DISTINCT f.sample_key) ${getDateFilter(
              "dt.full_date"
            )} as period_samples,
            COUNT(DISTINCT f.sample_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source = 'LIMS_Chemistry_Routine'
          AND f.detection_flag IN ('Not Detected', 'Unknown')
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 9,
          name: "Samples Tested Negative",
          type: "percentage",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Not Detected & Unknown",
        };
      },
      info: {
        id: 9,
        name: "Samples Tested Negative",
        description: "Samples with 'Not Detected' or 'Unknown' analytes",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "10": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
          if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as samples_not_started,
            COUNT(*) ${getDateFilter("ds.date_collected")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.dim_sample ds
          LEFT JOIN dwh.fact_test_results f ON ds.sample_key = f.sample_key
          WHERE f.sample_key IS NULL
          ${getWhereDate()}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 10,
          name: "Samples Not Started",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Backlog",
        };
      },
      info: {
        id: 10,
        name: "Samples Not Started",
        description:
          "Samples that have not been started (no test results) - can only filter by sample_type",
        category: "Sample Overview",
        parameters: ["level", "start_date", "end_date", "sample_type"],
      },
    },
    "11": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
          if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as samples_not_tested,
            COUNT(*) ${getDateFilter("ds.date_collected")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.dim_sample ds
          JOIN dwh.fact_test_results f ON ds.sample_key = f.sample_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          WHERE ds.sample_status = 'X'
          ${getWhereDate()}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 11,
          name: "Samples Not Tested",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Cancelled",
        };
      },
      info: {
        id: 11,
        name: "Samples Not Tested",
        description: "Samples with status 'X' (Cancelled)",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "location",
          "sample_type",
        ],
      },
    },
    "12": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(DISTINCT f.sample_key) as samples_authorized,
            COUNT(DISTINCT f.sample_key) ${getDateFilter(
              "dt.full_date"
            )} as period_samples,
            COUNT(DISTINCT f.sample_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.quality_flag = 'Approved'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 12,
          name: "Samples Authorized",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Finalized",
        };
      },
      info: {
        id: 12,
        name: "Samples Authorized",
        description: "Samples with quality_flag = 'Approved'",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "13": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(DISTINCT f.sample_key) as samples_not_authorized,
            COUNT(DISTINCT f.sample_key) ${getDateFilter(
              "dt.full_date"
            )} as period_samples,
            COUNT(DISTINCT f.sample_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.quality_flag IN ('Entered', 'Modified', 'Not Entered')
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 13,
          name: "Samples Not Authorized",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Awaiting Review",
        };
      },
      info: {
        id: 13,
        name: "Samples Not Authorized",
      },
    },
    "14": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as undetected_analytes,
            COUNT(*) ${getDateFilter("dt.full_date")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.detection_flag = 'Not Detected'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 14,
          name: "Undetected Analytes",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "<LOD",
        };
      },
      info: {
        id: 14,
        name: "Undetected Analytes",
      },
    },
    "15": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as detected_unquantifiable_analytes,
            COUNT(*) ${getDateFilter("dt.full_date")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.detection_flag = 'Unknown'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 15,
          name: "Detected but Unquantifiable Analytes",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "<BLOQ",
        };
      },
      info: {
        id: 15,
        name: "Detected but Unquantifiable Analytes",
        description:
          "Analytes detected but below limit of quantification (detection_flag = 'Unknown')",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },

    "16": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as detected_analytes,
            COUNT(*) ${getDateFilter("dt.full_date")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.detection_flag = 'Detected'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 16,
          name: "Detected Analytes",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Quantifiable",
        };
      },
      info: {
        id: 16,
        name: "Detected Analytes",
        description:
          "Analytes detected and quantifiable (detection_flag = 'Detected')",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "17": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as qualitative_outliers,
            COUNT(*) ${getDateFilter("dt.full_date")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.mart_anomalies ma
          JOIN dwh.dim_time dt ON ma.time_key = dt.time_key
          JOIN dwh.dim_analyte da ON ma.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON ma.location_key = dl.location_key
          WHERE ma.anomaly_type = 'qualitative'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 17,
          name: "Outliers in Qualitative Data",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Flagged Sample",
        };
      },
      info: {
        id: 17,
        name: "Outliers in Qualitative Data",
        description: "Qualitative anomalies detected in test results",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "18": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as quantitative_outliers,
            COUNT(*) ${getDateFilter("dt.full_date")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.mart_anomalies ma
          JOIN dwh.dim_time dt ON ma.time_key = dt.time_key
          JOIN dwh.dim_analyte da ON ma.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON ma.location_key = dl.location_key
          WHERE ma.anomaly_type = 'quantitative'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 18,
          name: "Outliers in Quantitative Data",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Flagged Sample",
        };
      },
      info: {
        id: 18,
        name: "Outliers in Quantitative Data",
        description: "Quantitative anomalies detected in test results",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "19": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            da.analyte_name,
            COUNT(*) as deviation_count,
            AVG(f.result_value) as avg_value,
            STDDEV(f.result_value) as std_dev
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.detection_flag = 'Detected'
          AND f.result_value IS NOT NULL
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          GROUP BY da.analyte_name
          HAVING COUNT(*) > 10
          ORDER BY deviation_count DESC
          LIMIT 5
        `);

        const totalDeviations = res.reduce(
          (sum, row) => sum + row.deviation_count,
          0
        );

        return {
          id: 19,
          name: "Analyte Tests Consistently High/Low",
          type: "multi_value",
          value: totalDeviations,
          change: {
            pct: 0,
            direction: "",
            prefix: "",
          },
          legend: "Deviation Monitoring",
          breakdown: res.map((row) => ({
            name: row.analyte_name,
            count: row.deviation_count,
            avg_value: row.avg_value,
            std_dev: row.std_dev,
          })),
        };
      },
      info: {
        id: 19,
        name: "Analyte Tests Consistently High/Low",
        description: "Analytes with consistent high/low concentration patterns",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "20": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as total_test_count,
            COUNT(*) ${getDateFilter("dt.full_date")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          JOIN dwh.dim_sample ds ON f.sample_key = ds.sample_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 20,
          name: "Test Count per Analyte (Aggregated)",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Total",
        };
      },
      info: {
        id: 20,
        name: "Test Count per Analyte (Aggregated)",
        description: "Total number of tests performed across all analytes",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
          "sample_type",
        ],
      },
    },
    "21": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
          if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as rejected_samples,
            COUNT(*) ${getDateFilter("ds.date_collected")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.dim_sample ds
          JOIN dwh.fact_test_results f ON ds.sample_key = f.sample_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          WHERE ds.sample_status = 'R'
          ${getWhereDate()}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 21,
          name: "Rejected Samples",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "QC Failure",
        };
      },
      info: {
        id: 21,
        name: "Rejected Samples",
        description:
          "Samples rejected due to quality control failures (sample_status = 'R')",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "location",
          "sample_type",
        ],
      },
    },
    "22": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (ds.date_collected >= '${start_date}')`;
          if (end_date) return `AND (ds.date_collected <= '${end_date}')`;
          return "";
        };

        const res = await query(`
          SELECT 
            COUNT(*) as cancelled_samples,
            COUNT(*) ${getDateFilter("ds.date_collected")} as period_samples,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE ds.date_collected BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_samples
          FROM dwh.dim_sample ds
          JOIN dwh.fact_test_results f ON ds.sample_key = f.sample_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          WHERE ds.sample_status = 'C'
          ${getWhereDate()}
          ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_samples: currentValue,
          comparison_samples: comparisonValue,
        } = res[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 22,
          name: "Cancelled Samples",
          type: "single_number",
          value: currentValue,
          change: {
            pct: Math.round(percentageChange * 100) / 100,
            direction:
              percentageChange > 0
                ? "up"
                : percentageChange < 0
                ? "down"
                : "center",
            prefix: "%",
          },
          legend: "Withdrawn by Request",
        };
      },
      info: {
        id: 22,
        name: "Cancelled Samples",
        description:
          "Samples cancelled/withdrawn by request (sample_status = 'C')",
        category: "Sample Overview",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "location",
          "sample_type",
        ],
      },
    },
  },
  risk_trends_cards: {
    "1": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const activeRes = await query(`
          SELECT 
            COUNT(DISTINCT mr.risk_key) as active_risks,
            COUNT(DISTINCT mr.risk_key) ${getDateFilter(
              "dt.full_date"
            )} as period_risks,
            COUNT(DISTINCT mr.risk_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_risks
          FROM dwh.mart_risks mr
          JOIN dwh.dim_time dt ON mr.time_key = dt.time_key
          JOIN dwh.dim_analyte da ON mr.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON mr.location_key = dl.location_key
          WHERE mr.risk_classification != 'Low'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const categoryRes = await query(`
          SELECT 
            mr.risk_category,
            COUNT(DISTINCT mr.risk_key) as risk_count
          FROM dwh.mart_risks mr
          JOIN dwh.dim_time dt ON mr.time_key = dt.time_key
          JOIN dwh.dim_analyte da ON mr.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON mr.location_key = dl.location_key
          WHERE mr.risk_classification != 'Low'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          GROUP BY mr.risk_category
          ORDER BY risk_count DESC
          LIMIT 3
        `);

        const {
          period_risks: currentValue,
          comparison_risks: comparisonValue,
        } = activeRes[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        const list = categoryRes.map((row) => ({
          name: row.risk_category || "Unknown",
          type: "two_numbers",
          left_side: {
            main: {
              value: row.risk_count,
              label: "Active",
            },
            legend: {
              type: "change",
              pct: 0,
              direction: "up",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: Math.round(row.risk_count * 1.2),
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 15,
              prefix: "%",
              label: "Uncertainty",
            },
          },
        }));

        return {
          id: 1,
          name: "Total Risk",
          type: "two_numbers",
          left_side: {
            main: {
              value: currentValue || 0,
              label: "Active",
            },
            legend: {
              type: "change",
              pct: Math.round(percentageChange * 100) / 100,
              direction:
                percentageChange > 0
                  ? "up"
                  : percentageChange < 0
                  ? "down"
                  : "center",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: Math.round((currentValue || 0) * 1.2),
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 14,
              prefix: "%",
              label: "Uncertainty",
            },
          },
          list: list,
        };
      },
      info: {
        id: 1,
        name: "Total Risk",
        description:
          "Active risks count and forecast (risk_classification != 'Low')",
        category: "Risk Trends",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "2": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const activeRes = await query(`
          SELECT 
            COUNT(DISTINCT mer.emerging_risk_key) as emerging_threats,
            COUNT(DISTINCT mer.emerging_risk_key) ${getDateFilter(
              "dt.full_date"
            )} as period_threats,
            COUNT(DISTINCT mer.emerging_risk_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_threats
          FROM dwh.mart_emerging_risks mer
          JOIN dwh.dim_time dt ON mer.time_key = dt.time_key
          JOIN dwh.dim_analyte da ON mer.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON mer.location_key = dl.location_key
          WHERE mer.response_status = 'active'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const categoryRes = await query(`
          SELECT 
            da.analyte_group,
            COUNT(DISTINCT mer.emerging_risk_key) as threat_count
          FROM dwh.mart_emerging_risks mer
          JOIN dwh.dim_time dt ON mer.time_key = dt.time_key
          JOIN dwh.dim_analyte da ON mer.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON mer.location_key = dl.location_key
          WHERE mer.response_status = 'active'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
          GROUP BY da.analyte_group
          ORDER BY threat_count DESC
          LIMIT 3
        `);

        const {
          period_threats: currentValue,
          comparison_threats: comparisonValue,
        } = activeRes[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        const list = categoryRes.map((row) => ({
          name: row.analyte_group || "Unknown",
          type: "two_numbers",
          left_side: {
            main: {
              value: row.threat_count,
              label: "Active",
            },
            legend: {
              type: "change",
              pct: 0,
              direction: "up",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: Math.round(row.threat_count * 1.3),
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 15,
              prefix: "%",
              label: "Uncertainty",
            },
          },
        }));

        return {
          id: 2,
          name: "Emerging Threats",
          type: "two_numbers",
          left_side: {
            main: {
              value: currentValue || 0,
              label: "Active",
            },
            legend: {
              type: "change",
              pct: Math.round(percentageChange * 100) / 100,
              direction:
                percentageChange > 0
                  ? "up"
                  : percentageChange < 0
                  ? "down"
                  : "center",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: Math.round((currentValue || 0) * 1.3),
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 67,
              prefix: "%",
              label: "Uncertainty",
            },
          },
          list: list,
        };
      },
      info: {
        id: 2,
        name: "Emerging Threats",
        description:
          "Emerging risks count and forecast (response_status = 'active')",
        category: "Risk Trends",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "3": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const activeRes = await query(`
          SELECT 
            COUNT(*) as radioactivity_risks,
            COUNT(*) ${getDateFilter("dt.full_date")} as period_risks,
            COUNT(*) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_risks
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.detection_flag = 'Detected'
          AND da.is_radioactive = TRUE
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_risks: currentValue,
          comparison_risks: comparisonValue,
        } = activeRes[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 3,
          name: "Radioactivity Risks",
          type: "two_numbers",
          left_side: {
            main: {
              value: currentValue || 0,
              label: "Active",
            },
            legend: {
              type: "change",
              pct: Math.round(percentageChange * 100) / 100,
              direction:
                percentageChange > 0
                  ? "up"
                  : percentageChange < 0
                  ? "down"
                  : "center",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: Math.round((currentValue || 0) * 1.3),
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 28,
              prefix: "%",
              label: "Uncertainty",
            },
          },
          list: null,
        };
      },
      info: {
        id: 3,
        name: "Radioactivity Risks",
        description:
          "Radioactive analyte risks count and forecast (is_radioactive = TRUE)",
        category: "Risk Trends",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
    "4": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const activeRes = await query(`
          SELECT 
            COUNT(DISTINCT dl.location_key) as critical_regions,
            COUNT(DISTINCT dl.location_key) ${getDateFilter(
              "dt.full_date"
            )} as period_regions,
            COUNT(DISTINCT dl.location_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_regions
          FROM dwh.mart_risks mr
          JOIN dwh.dim_time dt ON mr.time_key = dt.time_key
          JOIN dwh.dim_analyte da ON mr.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON mr.location_key = dl.location_key
          WHERE mr.risk_classification IN ('High', 'Critical')
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_regions: currentValue,
          comparison_regions: comparisonValue,
        } = activeRes[0] || {};
        const percentageChange =
          comparisonValue > 0
            ? ((currentValue - comparisonValue) / comparisonValue) * 100
            : 0;

        return {
          id: 4,
          name: "Critical Regions",
          type: "two_numbers",
          left_side: {
            main: {
              value: currentValue || 0,
              label: "Active",
            },
            legend: {
              type: "change",
              pct: Math.round(percentageChange * 100) / 100,
              direction:
                percentageChange > 0
                  ? "up"
                  : percentageChange < 0
                  ? "down"
                  : "center",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: Math.round((currentValue || 0) * 1.2),
              label: "Forecast",
            },
            legend: {
              type: "margin",
              amount: 4,
              prefix: "%",
              label: "Uncertainty",
            },
          },
          list: null,
        };
      },
      info: {
        id: 4,
        name: "Critical Regions",
        description:
          "Critical risk regions count and forecast (risk_classification IN ('High', 'Critical'))",
        category: "Risk Trends",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
  },
  analyte_insights_cards: {
    "1": {
      get: async (params: any) => {
        const {
          start_date,
          end_date,
          analyte,
          category,
          group,
          location,
          sample_type,
        } = params;

        const comparisonDates =
          start_date && end_date
            ? calculateComparisonDateRange(start_date, end_date)
            : null;

        const getDateFilter = (field: string) => {
          if (start_date && end_date)
            return `FILTER (WHERE ${field} BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `FILTER (WHERE ${field} >= '${start_date}')`;
          if (end_date) return `FILTER (WHERE ${field} <= '${end_date}')`;
          return "";
        };

        const getWhereDate = () => {
          if (start_date && end_date)
            return `AND (dt.full_date BETWEEN '${start_date}' AND '${end_date}')`;
          if (start_date) return `AND (dt.full_date >= '${start_date}')`;
          if (end_date) return `AND (dt.full_date <= '${end_date}')`;
          return "";
        };

        const presentRes = await query(`
          SELECT 
            COUNT(DISTINCT f.analyte_key) as present_analytes,
            COUNT(DISTINCT f.analyte_key) ${getDateFilter(
              "dt.full_date"
            )} as period_present,
            COUNT(DISTINCT f.analyte_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_present
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.detection_flag = 'Detected'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const absentRes = await query(`
          SELECT 
            COUNT(DISTINCT f.analyte_key) as absent_analytes,
            COUNT(DISTINCT f.analyte_key) ${getDateFilter(
              "dt.full_date"
            )} as period_absent,
            COUNT(DISTINCT f.analyte_key) ${
              comparisonDates?.comparison_start_date &&
              comparisonDates?.comparison_end_date
                ? `FILTER (WHERE dt.full_date BETWEEN '${comparisonDates.comparison_start_date}' AND '${comparisonDates.comparison_end_date}')`
                : ""
            } as comparison_absent
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          WHERE f.data_source IN ('chemistry_routine', 'LIMS_Chemistry_Routine')
          AND f.detection_flag = 'Not Detected'
          ${getWhereDate()}
          ${analyte ? `AND (da.analyte_name = '${analyte}')` : ""}
          ${category ? `AND (da.lab_classification = '${category}')` : ""}
          ${group ? `AND (da.analyte_group = '${group}')` : ""}
          ${location ? `AND (dl.location_name = '${location}')` : ""}
        `);

        const {
          period_present: presentCurrent,
          comparison_present: presentComparison,
        } = presentRes[0] || {};
        const {
          period_absent: absentCurrent,
          comparison_absent: absentComparison,
        } = absentRes[0] || {};

        const presentPercentageChange =
          presentComparison > 0
            ? ((presentCurrent - presentComparison) / presentComparison) * 100
            : 0;
        const absentPercentageChange =
          absentComparison > 0
            ? ((absentCurrent - absentComparison) / absentComparison) * 100
            : 0;

        return {
          id: 1,
          name: "Sample Presence",
          type: "two_numbers",
          left_side: {
            main: {
              value: presentCurrent || 0,
              label: "Present",
            },
            legend: {
              type: "change",
              pct: Math.round(presentPercentageChange * 100) / 100,
              direction:
                presentPercentageChange > 0
                  ? "up"
                  : presentPercentageChange < 0
                  ? "down"
                  : "center",
              prefix: "%",
            },
          },
          right_side: {
            main: {
              value: absentCurrent || 0,
              label: "Absent",
            },
            legend: {
              type: "change",
              pct: Math.round(absentPercentageChange * 100) / 100,
              direction:
                absentPercentageChange > 0
                  ? "up"
                  : absentPercentageChange < 0
                  ? "down"
                  : "center",
              prefix: "%",
            },
          },
          list: null,
        };
      },
      info: {
        id: 1,
        name: "Sample Presence",
        description:
          "Present vs Absent analytes count and comparison (detection_flag)",
        category: "Analyte Insights",
        parameters: [
          "level",
          "start_date",
          "end_date",
          "analyte",
          "category",
          "group",
          "location",
        ],
      },
    },
  },
};
