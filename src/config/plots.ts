import { query } from "../utils/db";

export const plotsConfig = [
  {
    collections: ["sample_overview_cards"],
    title: "Samples Collected By Location",
    card_ids: ["*", "1"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;
      const res = await query(`

          SELECT
            COUNT(DISTINCT ds.sample_id) as count , 
            dl.location_name
          FROM
              dwh.dim_sample ds
              inner join dwh.fact_test_results fts on ds.sample_key = fts.sample_key
              inner join dwh.dim_analyte a on a.analyte_key = fts.analyte_key
            inner join  dwh.dim_location dl  on fts.location_key = dl.location_key
          where 1=1 
        ${
          start_date && end_date
            ? `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`
            : ""
        }
        ${analyte ? `AND (a.analyte_name = '${analyte}')` : ""}
        ${category ? `AND (a.lab_classification = '${category}')` : ""}
        ${group ? `AND (a.analyte_group = '${group}')` : ""}
        ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}  
        ${location ? `AND (dl.location_name = '${location}')` : ""}
        group by dl.location_name
        `);

      return {
        id: "1",
        title: "Samples Collected By Location",
        type: "bar",
        data: res.map((item: any) => ({
          x: item.location_name,
          y: item.count,
        })),
      };
    },
  },
  {
    collections: ["sample_overview_cards"],
    card_ids: ["*", "1"],
    title: "Samples Collected Over Time",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week", // default to month if not specified
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      // Determine the date truncation based on date_frame
      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await query(`
          SELECT
            COUNT(DISTINCT ds.sample_id) as count,
            DATE_TRUNC('${dateTrunc}', ds.date_collected) as time_period,
            TO_CHAR(DATE_TRUNC('${dateTrunc}', ds.date_collected), 
              CASE 
                WHEN '${dateTrunc}' = 'week' THEN 'YYYY-"Week" WW'
                WHEN '${dateTrunc}' = 'month' THEN 'YYYY-MM'
                WHEN '${dateTrunc}' = 'quarter' THEN 'YYYY-"Q"Q'
                WHEN '${dateTrunc}' = 'year' THEN 'YYYY'
                ELSE 'YYYY-MM'
              END
            ) as formatted_period
          FROM
            dwh.dim_sample ds
            inner join dwh.fact_test_results fts on ds.sample_key = fts.sample_key
            inner join dwh.dim_analyte a on a.analyte_key = fts.analyte_key
            inner join dwh.dim_location dl on fts.location_key = dl.location_key
          WHERE 1=1 
            ${
              start_date && end_date
                ? `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`
                : ""
            }
            ${analyte ? `AND (a.analyte_name = '${analyte}')` : ""}
            ${category ? `AND (a.lab_classification = '${category}')` : ""}
            ${group ? `AND (a.analyte_group = '${group}')` : ""}
            ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}  
            ${location ? `AND (dl.location_name = '${location}')` : ""}
          GROUP BY DATE_TRUNC('${dateTrunc}', ds.date_collected)
          ORDER BY time_period ASC
          `);

      return {
        id: "2",
        title: `Samples Collected Over Time (${date_frame})`,
        type: "line", // Using line chart for time series data
        data: res.map((item: any) => ({
          x: item.formatted_period,
          y: parseInt(item.count),
        })),
      };
    },
  },
  {
    collections: ["risk_trends_cards"],
    card_ids: ["*", "1"],
    title: "Top 10 Locations with Most Risk",
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame = "week", // default to month if not specified
        analyte,
        category,
        group,
        sample_type,
        location,
      } = params;

      const res = await query(`

         SELECT 
            COUNT(DISTINCT f.sample_key) as count,
            dl.location_name
          FROM dwh.fact_test_results f
          JOIN dwh.dim_time dt ON f.sample_date_key = dt.time_key
          JOIN dwh.dim_analyte da ON f.analyte_key = da.analyte_key
          JOIN dwh.dim_location dl ON f.location_key = dl.location_key
          WHERE f.data_source = 'LIMS_Chemistry_Routine'
          AND f.detection_flag = 'Detected'
          AND da.is_regulated = TRUE
          ${
            start_date && end_date
              ? `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`
              : ""
          }
        ${analyte ? `AND (a.analyte_name = '${analyte}')` : ""}
        ${category ? `AND (a.lab_classification = '${category}')` : ""}
        ${group ? `AND (a.analyte_group = '${group}')` : ""}
        ${sample_type ? `AND (ds.sample_type = '${sample_type}')` : ""}  
        ${location ? `AND (dl.location_name = '${location}')` : ""}
        group by dl.location_name
          order by COUNT(DISTINCT f.sample_key) desc
          limit 10

          `);

      return {
        id: "3",
        title: `Top 10 Locations ًith Most Risk`,
        type: "bar", // Using line chart for time series data
        data: res.map((item: any) => ({
          x: item.location_name,
          y: parseInt(item.count),
        })),
      };
    },
  },
];
