import { query } from "../utils/db";

export const mapsConfig = [
  {
    collections: ["sample_overview_cards"],
    card_ids: ["*", "1"],
    get: async (params: any) => {
      const {
        start_date,
        end_date,
        card_id,
        collection,
        date_frame, // possible values: week, month, quarter, year
        location,
        analyte,
        category,
        group,
        sample_type,
      } = params;

      const getDateTrunc = (frame: string) => {
        switch (frame) {
          case "week":
            return "week";
          case "quarter":
            return "quarter";
          case "year":
            return "year";
          case "month":
          default:
            return "month";
        }
      };

      const dateTrunc = getDateTrunc(date_frame);

      const res = await query(`
          SELECT
            dl.catchment_name,
            ds.sample_id,
            COUNT(DISTINCT ds.sample_id) as count,
            DATE_TRUNC('${dateTrunc}', ds.date_collected) as time_period,
            TO_CHAR(DATE_TRUNC('${dateTrunc}', ds.date_collected), 
              CASE 
                WHEN '${dateTrunc}' = 'week' THEN 'YYYY-"Week" WW'
                WHEN '${dateTrunc}' = 'month' THEN 'YYYY-MM'
                WHEN '${dateTrunc}' = 'quarter' THEN 'YYYY-"Q"Q'
                WHEN '${dateTrunc}' = 'year' THEN 'YYYY'
                ELSE 'YYYY-MM'
              END
            ) as formatted_period
          FROM
            dwh.dim_sample ds
            inner join dwh.fact_test_results fts on ds.sample_key = fts.sample_key
            inner join dwh.dim_analyte a on a.analyte_key = fts.analyte_key
            inner join dwh.dim_location dl on fts.location_key = dl.location_key
            inner join dwh.dim_test dt on fts.test_key = dt.test_key
          WHERE 1=1 
            ${
              start_date && end_date
                ? `AND (ds.date_collected BETWEEN '${start_date}' AND '${end_date}')`
                : ""
            }
            ${analyte ? `AND (a.analyte_name = '${analyte}')` : ""}
            ${category ? `AND (a.lab_classification = '${category}')` : ""}
            ${group ? `AND (a.analyte_group = '${group}')` : ""}
            ${sample_type ? `AND (dt.test_type = '${sample_type}')` : ""}  
            ${location ? `AND (dl.location_name = '${location}')` : ""}
          GROUP BY DATE_TRUNC('${dateTrunc}', ds.date_collected) , dl.catchment_name, ds.sample_id
          ORDER BY time_period ASC
          `);

      console.log("dd", res);
      /**!SECTION
           * 
           *     {
        "count": "125",
        "time_period": "2023-01-02T00:00:00.000Z",
        "formatted_period": "2023-Week 01"
      },
      {
        "count": "50",
        "time_period": "2023-01-09T00:00:00.000Z",
        "formatted_period": "2023-Week 02"
      },
      {
        "count": "100",
        "time_period": "2023-01-16T00:00:00.000Z",
        "formatted_period": "2023-Week 03"
      },
           */
      /**!SECTION\\
           * {
        "date": "Q1-2024",
        "values": {
            "1": {},
            "1": {
                "risk_label": 1
            },
        }}
           */

   const groupedByFormattedPeriod = Object.values(
     res.reduce((acc: any, cur: any) => {
       if (!acc[cur.formatted_period]) {
         acc[cur.formatted_period] = {
           date: cur.formatted_period,
           values: {},
         };
       }

       acc[cur.formatted_period].values[cur.catchment_name] = {
       };

       return acc;
     }, {})
   );
      return groupedByFormattedPeriod;
    }
  }
];
