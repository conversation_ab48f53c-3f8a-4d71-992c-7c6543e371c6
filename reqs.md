#### add sample_point_id to dim_location (done)

#### add data to risk_scores & ai_forecasting_enhanced

#### make sure there is 5 levels of risks

#### add anaylyte type (enviromental, security, health) these are persona

sample_type
analyte_type
add persona to analyte group

---

flag on anomoly
monitor on risks

---APIS

-- catchmentArea for the location (map)

- get all alerts API (alert with location)
- flag anomlies
- risks api (with the analyte card)
  routine vs ad-hoc -> (dim_test.test_type) is for the last filter

--- for infograph
micropology vs chem -- question for raed

<!-- UI task -->

send persona (enviromental, security, health) for the risk
